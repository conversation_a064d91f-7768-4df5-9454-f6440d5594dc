'use client';

import { useState } from 'react';
import TopBar from '../../components/TopBar';
import Footer from '../../components/Footer';
import MainContainer from '../../components/MainContainer';
import CookieConsent from '../../components/CookieConsent';
import Image from 'next/image';

interface FormData {
  name: string;
  email: string;
  phone: string;
  date: string;
  time: string;
  guests: string;
  notes: string;
}

interface FormErrors {
  [key: string]: string;
}

export default function Prenotazione() {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    date: '',
    time: '',
    guests: '2',
    notes: ''
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Il nome è obbligatorio';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'L\'email è obbligatoria';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Inserisci un\'email valida';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Il telefono è obbligatorio';
    }

    if (!formData.date) {
      newErrors.date = 'La data è obbligatoria';
    } else {
      const selectedDate = new Date(formData.date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      if (selectedDate < today) {
        newErrors.date = 'Seleziona una data futura';
      }
    }

    if (!formData.time) {
      newErrors.time = 'L\'orario è obbligatorio';
    }

    if (!formData.guests || parseInt(formData.guests) < 1) {
      newErrors.guests = 'Seleziona il numero di ospiti';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Here you would typically send the data to your backend
      // For now, we'll simulate the email sending
      const emailBody = `
        Nuova prenotazione da ${formData.name}
        
        Dettagli prenotazione:
        - Nome: ${formData.name}
        - Email: ${formData.email}
        - Telefono: ${formData.phone}
        - Data: ${formData.date}
        - Orario: ${formData.time}
        - Numero ospiti: ${formData.guests}
        - Note aggiuntive: ${formData.notes || 'Nessuna'}
      `;

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('Reservation data:', formData);
      console.log('Email body:', emailBody);
      
      setIsSubmitted(true);
    } catch (error) {
      console.error('Error submitting reservation:', error);
      alert('Si è verificato un errore. Riprova più tardi.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  if (isSubmitted) {
    return (
      <>
        <TopBar />
        <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8 min-h-screen flex items-center">
          <div className="max-w-2xl mx-auto text-center">
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-8">
              <h2 className="font-heading text-2xl font-semibold mb-4">
                Prenotazione Inviata!
              </h2>
              <p className="mb-4">
                Grazie {formData.name}! La tua prenotazione è stata inviata con successo.
              </p>
              <p className="text-sm">
                Ti contatteremo presto al numero {formData.phone} per confermare la prenotazione.
              </p>
            </div>
            <button
              onClick={() => {
                setIsSubmitted(false);
                setFormData({
                  name: '',
                  email: '',
                  phone: '',
                  date: '',
                  time: '',
                  guests: '2',
                  notes: ''
                });
              }}
              className="btn-romoletto"
            >
              Nuova Prenotazione
            </button>
          </div>
        </section>
        <Footer />
        <CookieConsent />
      </>
    );
  }

  return (
    <>
      <TopBar />

      {/* Hero Image - Full Width */}
      <section className="relative h-[500px] w-full overflow-hidden">
        <Image
          src="/pagine/prenota-tavolo.jpg"
          alt="Prenota il Tuo Tavolo - Romoletto"
          fill
          className="object-cover object-center"
          priority
        />
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        <div className="absolute inset-0 flex items-center justify-center pt-16">
          <div className="text-center text-white px-4">
            <h1 className="font-heading text-4xl md:text-5xl font-bold mb-4">
              Prenota il Tuo Tavolo
            </h1>
            <div className="w-24 h-1 bg-primary-yellow mx-auto mb-4"></div>
            <p className="text-lg md:text-xl leading-relaxed max-w-2xl mx-auto">
              Vieni a gustare l'autentica cucina romana in un'atmosfera familiare
            </p>
          </div>
        </div>
      </section>

      {/* Reservation Form - Boxed */}
      <MainContainer>
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          {/* Immagine atmosfera ristorante */}
          <div className="relative h-80 rounded-lg overflow-hidden shadow-xl mb-12">
            <Image
              src="/piatti/DSC02883.jpg"
              alt="Atmosfera accogliente del ristorante Romoletto"
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
            />
          </div>

        <div className="max-w-2xl mx-auto">
          <form onSubmit={handleSubmit} className="space-y-6">
            
            {/* Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-dark-gray mb-2">
                Nome Completo *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-red focus:border-transparent ${
                  errors.name ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Il tuo nome completo"
              />
              {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
            </div>

            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-dark-gray mb-2">
                Email *
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-red focus:border-transparent ${
                  errors.email ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="<EMAIL>"
              />
              {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
            </div>

            {/* Phone */}
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-dark-gray mb-2">
                Telefono *
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-red focus:border-transparent ${
                  errors.phone ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="+39 ************"
              />
              {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
            </div>

            {/* Date and Time */}
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="date" className="block text-sm font-medium text-dark-gray mb-2">
                  Data *
                </label>
                <input
                  type="date"
                  id="date"
                  name="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  min={new Date().toISOString().split('T')[0]}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-red focus:border-transparent ${
                    errors.date ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.date && <p className="text-red-500 text-sm mt-1">{errors.date}</p>}
              </div>

              <div>
                <label htmlFor="time" className="block text-sm font-medium text-dark-gray mb-2">
                  Orario *
                </label>
                <select
                  id="time"
                  name="time"
                  value={formData.time}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-red focus:border-transparent ${
                    errors.time ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">Seleziona orario</option>
                  <option value="12:00">12:00</option>
                  <option value="12:30">12:30</option>
                  <option value="13:00">13:00</option>
                  <option value="13:30">13:30</option>
                  <option value="14:00">14:00</option>
                  <option value="14:30">14:30</option>
                  <option value="19:00">19:00</option>
                  <option value="19:30">19:30</option>
                  <option value="20:00">20:00</option>
                  <option value="20:30">20:30</option>
                  <option value="21:00">21:00</option>
                  <option value="21:30">21:30</option>
                  <option value="22:00">22:00</option>
                  <option value="22:30">22:30</option>
                </select>
                {errors.time && <p className="text-red-500 text-sm mt-1">{errors.time}</p>}
              </div>
            </div>

            {/* Number of Guests */}
            <div>
              <label htmlFor="guests" className="block text-sm font-medium text-dark-gray mb-2">
                Numero di Ospiti *
              </label>
              <select
                id="guests"
                name="guests"
                value={formData.guests}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-red focus:border-transparent ${
                  errors.guests ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                {[...Array(12)].map((_, i) => (
                  <option key={i + 1} value={i + 1}>
                    {i + 1} {i + 1 === 1 ? 'persona' : 'persone'}
                  </option>
                ))}
                <option value="13+">13+ persone (contattaci)</option>
              </select>
              {errors.guests && <p className="text-red-500 text-sm mt-1">{errors.guests}</p>}
            </div>

            {/* Notes */}
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-dark-gray mb-2">
                Note Aggiuntive
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-red focus:border-transparent"
                placeholder="Allergie, richieste speciali, occasioni particolari..."
              />
            </div>

            {/* Submit Button */}
            <div className="text-center">
              <button
                type="submit"
                disabled={isSubmitting}
                className={`btn-romoletto text-lg px-8 py-4 ${
                  isSubmitting ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105 transform transition-all duration-300'
                }`}
              >
                {isSubmitting ? 'Invio in corso...' : 'Invia Prenotazione'}
              </button>
            </div>

            <p className="text-sm text-gray-600 text-center">
              * Campi obbligatori. Ti contatteremo per confermare la disponibilità.
            </p>
          </form>
        </div>
        </section>
      </MainContainer>

      {/* Footer - Full Width */}
      <Footer />
      <CookieConsent />
    </>
  );
}
