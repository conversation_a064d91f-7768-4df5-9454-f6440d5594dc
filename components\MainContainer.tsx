import { ReactNode } from 'react';

interface MainContainerProps {
  children: ReactNode;
  className?: string;
}

export default function MainContainer({ children, className = '' }: MainContainerProps) {
  return (
    <div className={`pattern-bg min-h-screen ${className}`}>
      <div className="max-w-7xl mx-auto bg-white shadow-2xl border-l-4 border-r-4 border-dark-gray min-h-screen">
        {children}
      </div>
    </div>
  );
}
