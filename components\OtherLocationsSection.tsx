import RestaurantCard from './RestaurantCard';

export default function OtherLocationsSection() {
  const restaurants = [
    {
      name: 'Mariucci<PERSON>',
      address: 'Via del Corso, 123',
      location: 'Centro Storico, Roma',
      imageSrc: '/ristoranti/mariuccia.webp',
      menuLink: '/menu-mariuccia', // Sostituisci con il link effettivo
      reservationLink: '/prenotazione-mariuccia' // Sostituisci con il link effettivo
    },
    {
      name: 'R<PERSON>lett<PERSON>',
      address: 'Piazza Campo de\' Fiori, 47',
      location: 'Campo de\' Fiori, Roma',
      imageSrc: '/ristoranti/romoletto.webp',
      menuLink: '/menu-romoletto', // Sostituisci con il link effettivo
      reservationLink: '/prenotazione-romoletto' // Sostituisci con il link effettivo
    }
  ];

  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="font-heading text-4xl font-bold text-primary-red mb-6">
            Le Nostre Sedi
          </h2>
          <div className="w-24 h-1 bg-primary-yellow mx-auto mb-8"></div>
          <p className="text-lg text-dark-gray max-w-3xl mx-auto">
            Scopri i nostri ristoranti nel cuore di Roma. Ogni sede mantiene 
            l'autenticità della tradizione culinaria romana con la sua atmosfera unica.
          </p>
        </div>

        {/* Restaurant Cards Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
          {restaurants.map((restaurant, index) => (
            <RestaurantCard
              key={index}
              name={restaurant.name}
              address={restaurant.address}
              location={restaurant.location}
              imageSrc={restaurant.imageSrc}
              menuLink={restaurant.menuLink}
              reservationLink={restaurant.reservationLink}
            />
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <div className="bg-primary-yellow p-8 rounded-lg">
            <h3 className="font-heading text-2xl font-semibold text-primary-red mb-4">
              Vieni a trovarci!
            </h3>
            <p className="text-lg text-dark-gray mb-6">
              Scegli la sede più comoda per te e vieni a gustare 
              l'autentica cucina romana in un'atmosfera familiare.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
