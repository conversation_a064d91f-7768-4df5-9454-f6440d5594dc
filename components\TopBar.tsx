'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';

export default function TopBar() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMobileMenu = () => {
    const newState = !isMobileMenuOpen;
    setIsMobileMenuOpen(newState);

    // Google Analytics event tracking
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', newState ? 'menu_open' : 'menu_close', {
        event_category: 'navigation',
        event_label: 'sidebar_menu'
      });
    }
  };

  return (
    <>
      <nav
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          isScrolled
            ? 'bg-primary-yellow shadow-lg'
            : 'bg-transparent'
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Left side - Prenota Tavolo Button */}
            <div className="flex items-center">
              <Link
                href="/prenotazione"
                className="bg-primary-red text-primary-yellow px-6 py-2 rounded-lg font-semibold hover:bg-primary-yellow hover:text-primary-red transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                Prenota Tavolo
              </Link>
            </div>

            {/* Center - Logo */}
            <Link href="/" className="flex-shrink-0">
              <Image
                src="/logo/logo_giallo_topbar.png"
                alt="Romoletto Logo"
                width={120}
                height={40}
                className="h-10 w-auto"
                priority
              />
            </Link>

            {/* Right side - Menu button */}
            <div className="flex items-center">
              <button
                onClick={toggleMobileMenu}
                className={`inline-flex items-center justify-center p-2 rounded-md transition-all duration-300 hover:scale-110 ${
                  isScrolled
                    ? 'text-primary-red hover:text-supporting-dark-red hover:bg-primary-yellow hover:bg-opacity-20'
                    : 'text-primary-yellow hover:text-off-white hover:bg-white hover:bg-opacity-20'
                }`}
                aria-expanded="false"
              >
                <span className="sr-only">Apri menu principale</span>
                {/* Hamburger icon */}
                <svg
                  className="h-6 w-6"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Sidebar for both Desktop and Mobile */}
      <div
        className={`fixed inset-0 z-50 transform transition-transform duration-300 ease-in-out ${
          isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        {/* Backdrop */}
        <div
          className="absolute inset-0 bg-black bg-opacity-50"
          onClick={toggleMobileMenu}
        ></div>

        {/* Sidebar */}
        <div className="absolute right-0 top-0 h-full w-80 bg-primary-yellow shadow-xl">
          <div className="flex flex-col h-full">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-primary-red">
              <Image
                src="/logo/logo_giallo_topbar.png"
                alt="Romoletto Logo"
                width={100}
                height={33}
                className="h-8 w-auto"
              />
              <button
                onClick={toggleMobileMenu}
                className="text-primary-red hover:text-supporting-dark-red p-2"
              >
                <svg
                  className="h-6 w-6"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Navigation Links */}
            <nav className="flex-1 px-4 py-6 space-y-2">
              <Link
                href="/nostra-storia"
                className="group block text-primary-red hover:text-supporting-dark-red font-semibold text-lg py-4 px-4 rounded-lg border-b border-primary-red border-opacity-20 hover:bg-primary-red hover:bg-opacity-10 transition-all duration-300 hover:translate-x-2"
                onClick={toggleMobileMenu}
              >
                <span className="flex items-center">
                  La Nostra Storia
                  <span className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300">→</span>
                </span>
              </Link>
              <button
                onClick={() => {
                  window.open('/pdf/menu_romoletto.pdf', '_blank');
                  toggleMobileMenu();
                }}
                className="group block w-full text-left text-primary-red hover:text-supporting-dark-red font-semibold text-lg py-4 px-4 rounded-lg border-b border-primary-red border-opacity-20 hover:bg-primary-red hover:bg-opacity-10 transition-all duration-300 hover:translate-x-2"
              >
                <span className="flex items-center">
                  Menu
                  <span className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300">↗</span>
                </span>
              </button>
              <Link
                href="/lavora-con-noi"
                className="group block text-primary-red hover:text-supporting-dark-red font-semibold text-lg py-4 px-4 rounded-lg border-b border-primary-red border-opacity-20 hover:bg-primary-red hover:bg-opacity-10 transition-all duration-300 hover:translate-x-2"
                onClick={toggleMobileMenu}
              >
                <span className="flex items-center">
                  Lavora con Noi
                  <span className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300">→</span>
                </span>
              </Link>
              <Link
                href="/prenotazione"
                className="group block text-primary-red hover:text-supporting-dark-red font-semibold text-lg py-4 px-4 rounded-lg border-b border-primary-red border-opacity-20 hover:bg-primary-red hover:bg-opacity-10 transition-all duration-300 hover:translate-x-2"
                onClick={toggleMobileMenu}
              >
                <span className="flex items-center">
                  Prenotazione
                  <span className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300">→</span>
                </span>
              </Link>
            </nav>

            {/* CTA Button */}
            <div className="p-4">
              <Link
                href="/prenotazione"
                className="bg-primary-red text-primary-yellow px-6 py-3 rounded-lg font-semibold hover:bg-primary-yellow hover:text-primary-red transition-all duration-300 shadow-lg hover:shadow-xl w-full text-center block"
                onClick={toggleMobileMenu}
              >
                Prenota Ora
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
