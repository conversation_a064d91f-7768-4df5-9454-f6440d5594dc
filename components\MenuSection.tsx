'use client';

import { useState } from 'react';
import Image from 'next/image';

export default function MenuSection() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const openMenu = () => {
    // Open PDF in new tab
    window.open('/pdf/menu_romoletto.pdf', '_blank');
  };

  return (
    <section className="py-16 bg-primary-yellow">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="font-heading text-4xl font-bold text-primary-red mb-6">
            Il Nostro Menu
          </h2>
          <p className="text-lg text-dark-gray mb-8 max-w-3xl mx-auto">
            Scopri i sapori autentici della cucina romana. Dalle paste tradizionali 
            alle pizze croccanti, ogni piatto è preparato con ingredienti freschi 
            e ricette tramandate di generazione in generazione.
          </p>

          {/* Menu highlights */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div className="text-center">
              <div className="relative w-32 h-24 mx-auto mb-4 rounded-lg overflow-hidden shadow-lg">
                <Image
                  src="/piatti/DSC02940.jpg"
                  alt="Primi Piatti - Cacio e pepe"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 50vw, 25vw"
                />
              </div>
              <h3 className="font-heading text-xl font-semibold text-primary-red mb-2">
                Primi Piatti
              </h3>
              <p className="text-dark-gray">
                Cacio e pepe, carbonara, amatriciana e altri classici romani
              </p>
            </div>

            <div className="text-center">
              <div className="relative w-32 h-24 mx-auto mb-4 rounded-lg overflow-hidden shadow-lg">
                <Image
                  src="/piatti/DSC02891.jpg"
                  alt="Pizza Romana croccante"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 50vw, 25vw"
                />
              </div>
              <h3 className="font-heading text-xl font-semibold text-primary-red mb-2">
                Pizza Romana
              </h3>
              <p className="text-dark-gray">
                Pizza bassa e croccante con ingredienti di prima qualità
              </p>
            </div>

            <div className="text-center">
              <div className="relative w-32 h-24 mx-auto mb-4 rounded-lg overflow-hidden shadow-lg">
                <Image
                  src="/piatti/DSC02875.jpg"
                  alt="Secondi piatti tradizionali"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 50vw, 25vw"
                />
              </div>
              <h3 className="font-heading text-xl font-semibold text-primary-red mb-2">
                Secondi & Contorni
              </h3>
              <p className="text-dark-gray">
                Coda alla vaccinara, saltimbocca e verdure di stagione
              </p>
            </div>
          </div>

          {/* CTA Button */}
          <button
            onClick={openMenu}
            className="bg-primary-red text-primary-yellow px-8 py-4 text-lg rounded-lg font-semibold hover:bg-primary-yellow hover:text-primary-red transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 transform"
          >
            Scarica il Menu Completo
          </button>

          <p className="text-sm text-dark-gray mt-4">
            Clicca per visualizzare il menu completo in PDF
          </p>
        </div>
      </div>
    </section>
  );
}
