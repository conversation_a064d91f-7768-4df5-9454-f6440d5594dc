import TopBar from '../../components/TopBar';
import Footer from '../../components/Footer';
import MainContainer from '../../components/MainContainer';
import CookieConsent from '../../components/CookieConsent';
import Image from 'next/image';

export default function LavoraConNoi() {
  return (
    <>
      <TopBar />

      {/* Hero Image - Full Width */}
      <section className="relative h-[500px] w-full overflow-hidden">
        <Image
          src="/pagine/lavora-con-noi.jpg"
          alt="Lavora con Noi - Romoletto"
          fill
          className="object-cover object-center"
          priority
        />
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        <div className="absolute inset-0 flex items-center justify-center pt-16">
          <div className="text-center text-white px-4">
            <h1 className="font-heading text-4xl md:text-5xl font-bold mb-4">
              Lavora con Noi
            </h1>
            <div className="w-24 h-1 bg-primary-yellow mx-auto mb-4"></div>
            <p className="text-lg md:text-xl leading-relaxed max-w-2xl mx-auto">
              Vuoi entrare nella famiglia di Mariuccia e Romoletto?
            </p>
          </div>
        </div>
      </section>

      {/* Main Content - Boxed */}
      <MainContainer>
        <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          
          {/* Introduction */}
          <div className="text-center mb-12">
            <p className="text-lg text-dark-gray leading-relaxed mb-8">
              Siamo sempre alla ricerca di persone sorridenti, motivate e appassionate di cucina, 
              accoglienza e lavoro di squadra.
            </p>
            <p className="text-lg text-dark-gray leading-relaxed">
              Crediamo in una ristorazione fatta con il cuore ma anche con la testa, dove il cliente 
              si sente a casa e chi lavora con noi si sente valorizzato ogni giorno.
            </p>
          </div>

          {/* What We Look For */}
          <div className="bg-off-white p-8 rounded-lg mb-12">
            <h2 className="font-heading text-3xl font-semibold text-primary-red mb-8 text-center">
              Se...
            </h2>
            
            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="bg-primary-red text-primary-yellow rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-sm font-bold">✓</span>
                  </div>
                  <p className="text-dark-gray">
                    <strong>ami il contatto con le persone</strong>
                  </p>
                </div>
                
                <div className="flex items-start space-x-4">
                  <div className="bg-primary-red text-primary-yellow rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-sm font-bold">✓</span>
                  </div>
                  <p className="text-dark-gray">
                    <strong>hai voglia di crescere</strong> in un ambiente dinamico
                  </p>
                </div>
              </div>
              
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="bg-primary-red text-primary-yellow rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-sm font-bold">✓</span>
                  </div>
                  <p className="text-dark-gray">
                    per te <strong>il servizio è un piacere</strong> e non un obbligo
                  </p>
                </div>
                
                <div className="flex items-start space-x-4">
                  <div className="bg-primary-red text-primary-yellow rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-sm font-bold">✓</span>
                  </div>
                  <p className="text-dark-gray">
                    <strong>ti piace lavorare in squadra</strong>, ma anche metterci del tuo
                  </p>
                </div>
              </div>
            </div>

            <div className="text-center mt-8">
              <p className="text-xl font-semibold text-primary-red">
                Allora potresti essere la persona giusta per noi!
              </p>
            </div>
          </div>

          {/* Open Positions */}
          <div className="mb-12">
            <h2 className="font-heading text-3xl font-semibold text-primary-red mb-8 text-center">
              Posizioni Aperte
            </h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              {/* Kitchen Staff */}
              <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow">
                <div className="flex items-center mb-4">
                  <div className="bg-primary-yellow text-primary-red rounded-full w-12 h-12 flex items-center justify-center mr-4">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                    </svg>
                  </div>
                  <h3 className="font-heading text-xl font-semibold text-dark-gray">
                    Personale di Cucina
                  </h3>
                </div>
                <p className="text-gray-600 mb-4">
                  Cerchiamo cuochi e aiuto cuochi appassionati della tradizione romana e napoletana.
                </p>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Esperienza in cucina tradizionale</li>
                  <li>• Passione per ingredienti di qualità</li>
                  <li>• Capacità di lavorare in team</li>
                </ul>
              </div>

              {/* Service Staff */}
              <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow">
                <div className="flex items-center mb-4">
                  <div className="bg-primary-yellow text-primary-red rounded-full w-12 h-12 flex items-center justify-center mr-4">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h3 className="font-heading text-xl font-semibold text-dark-gray">
                    Personale di Sala
                  </h3>
                </div>
                <p className="text-gray-600 mb-4">
                  Cerchiamo camerieri e baristi che amino l'accoglienza e il servizio di qualità.
                </p>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Esperienza nel servizio ristorativo</li>
                  <li>• Attitudine al cliente</li>
                  <li>• Conoscenza vini (preferibile)</li>
                </ul>
              </div>

              {/* Management */}
              <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow">
                <div className="flex items-center mb-4">
                  <div className="bg-primary-yellow text-primary-red rounded-full w-12 h-12 flex items-center justify-center mr-4">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h3 className="font-heading text-xl font-semibold text-dark-gray">
                    Responsabili
                  </h3>
                </div>
                <p className="text-gray-600 mb-4">
                  Cerchiamo figure di coordinamento per sala e cucina con esperienza gestionale.
                </p>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Esperienza nella gestione team</li>
                  <li>• Leadership e problem solving</li>
                  <li>• Conoscenza settore ristorazione</li>
                </ul>
              </div>

              {/* Part-time */}
              <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow">
                <div className="flex items-center mb-4">
                  <div className="bg-primary-yellow text-primary-red rounded-full w-12 h-12 flex items-center justify-center mr-4">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h3 className="font-heading text-xl font-semibold text-dark-gray">
                    Part-time & Weekend
                  </h3>
                </div>
                <p className="text-gray-600 mb-4">
                  Opportunità flessibili per studenti e chi cerca orari ridotti.
                </p>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Orari flessibili</li>
                  <li>• Ideale per studenti</li>
                  <li>• Formazione inclusa</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Immagine team - Full Width */}
          <div className="relative h-80 rounded-lg overflow-hidden shadow-xl mb-12">
            <Image
              src="/piatti/DSC02906.jpg"
              alt="Il team di Romoletto al lavoro"
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
            />
          </div>

          {/* What We Offer */}
          <div className="bg-primary-red text-primary-yellow p-8 rounded-lg mb-12">
            <h2 className="font-heading text-3xl font-semibold mb-8 text-center">
              Cosa Offriamo
            </h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">💰</span>
                  <span>Retribuzione competitiva</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">📚</span>
                  <span>Formazione continua</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">🎯</span>
                  <span>Opportunità di crescita</span>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">👥</span>
                  <span>Ambiente di lavoro familiare</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">🍽️</span>
                  <span>Pasti inclusi durante il turno</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">⏰</span>
                  <span>Orari rispettosi della vita privata</span>
                </div>
              </div>
            </div>
          </div>

          {/* How to Apply */}
          <div className="text-center">
            <h2 className="font-heading text-3xl font-semibold text-primary-red mb-8">
              Come Candidarsi
            </h2>
            
            <div className="bg-off-white p-8 rounded-lg mb-8">
              <p className="text-lg text-dark-gray mb-6">
                Invia il tuo CV e una breve lettera di presentazione a:
              </p>
              
              <div className="space-y-4">
                <div className="flex items-center justify-center space-x-3">
                  <svg className="w-6 h-6 text-primary-red" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-xl font-semibold text-primary-red hover:text-supporting-dark-red transition-colors"
                  >
                    <EMAIL>
                  </a>
                </div>
                
                <div className="flex items-center justify-center space-x-3">
                  <svg className="w-6 h-6 text-primary-red" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                  </svg>
                  <span className="text-xl font-semibold text-primary-red">
                    +39 06 XXXX XXXX
                  </span>
                </div>
              </div>
            </div>

            <p className="text-gray-600 mb-8">
              Oppure passa direttamente in ristorante durante gli orari di apertura per un colloquio informale!
            </p>

            <div className="bg-primary-yellow p-6 rounded-lg">
              <p className="text-primary-red font-semibold text-lg">
                Ti aspettiamo nella famiglia Romoletto! 🍝❤️
              </p>
            </div>
          </div>

        </div>
        </section>
      </MainContainer>

      {/* Footer - Full Width */}
      <Footer />
      <CookieConsent />
    </>
  );
}
