import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>, Source_Sans_3 } from "next/font/google";
import "./globals.css";

const oswald = <PERSON>({
  variable: "--font-oswald",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

const sourceSans = Source_Sans_3({
  variable: "--font-source-sans",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "Romoletto - Ristorante Pizzeria Roma",
  description: "Romoletto: autentica cucina romana nel cuore di Roma. Pizza, pasta e tradizione in un'atmosfera familiare. Prenota il tuo tavolo.",
  keywords: "ristorante roma, pizzeria roma, cucina romana, cacio e pepe, carbonara, amatriciana, pizza romana, trastevere",
  authors: [{ name: "<PERSON><PERSON><PERSON><PERSON>" }],
  openGraph: {
    title: "Romoletto - Ristorante Pizzeria Roma",
    description: "Autentica cucina romana nel cuore di Roma",
    type: "website",
    locale: "it_IT",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="it">
      <head>
        <link rel="icon" href="/favicon-for-public/web-app-manifest-192x192.png" />
        <link rel="apple-touch-icon" href="/favicon-for-app/apple-icon.png" />

        {/* Google Analytics */}
        <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}

              // Set default consent mode
              gtag('consent', 'default', {
                'analytics_storage': 'denied',
                'ad_storage': 'denied',
                'ad_user_data': 'denied',
                'ad_personalization': 'denied',
                'wait_for_update': 500,
              });

              gtag('js', new Date());
              gtag('config', 'GA_MEASUREMENT_ID', {
                cookie_flags: 'SameSite=None;Secure'
              });

              // Track page views
              gtag('event', 'page_view', {
                page_title: document.title,
                page_location: window.location.href
              });

              // Track scroll events
              let scrollTracked = false;
              window.addEventListener('scroll', function() {
                if (!scrollTracked && window.scrollY > 100) {
                  gtag('event', 'scroll', {
                    event_category: 'engagement',
                    event_label: 'page_scroll_100px'
                  });
                  scrollTracked = true;
                }
              });
            `,
          }}
        />
      </head>
      <body
        className={`${oswald.variable} ${sourceSans.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
